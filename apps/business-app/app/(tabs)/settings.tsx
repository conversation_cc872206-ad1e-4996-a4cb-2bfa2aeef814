import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, ScrollView, Pressable } from 'react-native';
import { router } from 'expo-router';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Input, InputField } from '@indie-points/ui-input';
import { Spinner } from '@indie-points/ui-spinner';
import { useAuth } from '@indie-points/contexts';
import {
  BusinessService,
  BusinessProfile,
  BusinessReward,
} from '../../services';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { GradientBar } from '@indie-points/auth';
import * as Haptics from 'expo-haptics';

export default function Settings() {
  const { user, signOut } = useAuth();
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [businessName, setBusinessName] = useState('');
  const [businessType, setBusinessType] = useState('');

  // Rewards state
  const [rewards, setRewards] = useState<BusinessReward[]>([]);
  const [loadingRewards, setLoadingRewards] = useState(false);
  const [showAddReward, setShowAddReward] = useState(false);
  const [editingReward, setEditingReward] = useState<BusinessReward | null>(
    null
  );
  const [rewardTitle, setRewardTitle] = useState('');
  const [rewardDescription, setRewardDescription] = useState('');
  const [rewardPoints, setRewardPoints] = useState('');

  // Fetch business profile and rewards
  useEffect(() => {
    const fetchData = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      const result = await BusinessService.getBusinessProfile(user.id);

      if (result.error) {
        setError(result.error);
        setBusinessProfile(null);
        // Set default values for new profile
        setBusinessName('');
        setBusinessType('');
      } else if (!result.data) {
        // No profile exists, set defaults for creation
        setBusinessProfile(null);
        setBusinessName('');
        setBusinessType('');
        setEditing(true); // Start in editing mode if no profile exists
      } else {
        // Profile exists, populate form
        setBusinessProfile(result.data);
        setBusinessName(result.data.businessName);
        setBusinessType(result.data.businessType);

        // Fetch rewards for this business
        await fetchRewards(result.data.id);
      }

      setLoading(false);
    };

    fetchData();
  }, [user?.id]);

  const fetchRewards = async (businessId: string) => {
    setLoadingRewards(true);
    try {
      const result = await BusinessService.getBusinessRewards(businessId);
      if (result.error) {
        console.error('Error fetching rewards:', result.error);
      } else {
        setRewards(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching rewards:', error);
    } finally {
      setLoadingRewards(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    const name = businessName.trim();
    const type = businessType.trim();

    if (!name || !type) {
      setError('Please fill in all required fields.');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const result = await BusinessService.upsertBusinessProfile(user.id, {
        businessName: name,
        businessType: type,
        onboardingCompleted: true,
        onboardingStep: 4,
      });

      if (result.error) {
        setError(result.error);
      } else if (result.data) {
        setBusinessProfile(result.data);
        setEditing(false);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Fetch rewards if this is a new profile
        if (rewards.length === 0) {
          await fetchRewards(result.data.id);
        }
      }
    } catch (error) {
      console.error('Error saving business profile:', error);
      setError('Failed to save business profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleAddReward = () => {
    setShowAddReward(true);
    setEditingReward(null);
    setRewardTitle('');
    setRewardDescription('');
    setRewardPoints('');
  };

  const handleEditReward = (reward: BusinessReward) => {
    setEditingReward(reward);
    setShowAddReward(true);
    setRewardTitle(reward.title);
    setRewardDescription(reward.description);
    setRewardPoints(reward.pointsRequired.toString());
  };

  const handleSaveReward = async () => {
    if (!businessProfile?.id) return;

    const title = rewardTitle.trim();
    const description = rewardDescription.trim();
    const points = parseInt(rewardPoints);

    if (!title || !description || isNaN(points) || points <= 0) {
      setError('Please fill in all reward fields with valid values.');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      let result;
      if (editingReward) {
        // Update existing reward
        result = await BusinessService.updateBusinessReward(editingReward.id, {
          title,
          description,
          pointsRequired: points,
          isActive: true,
        });
      } else {
        // Create new reward
        result = await BusinessService.createBusinessReward(
          businessProfile.id,
          {
            title,
            description,
            pointsRequired: points,
            isActive: true,
          }
        );
      }

      if (result.error) {
        setError(result.error);
      } else {
        setShowAddReward(false);
        setEditingReward(null);
        await fetchRewards(businessProfile.id);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Error saving reward:', error);
      setError('Failed to save reward. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteReward = async (rewardId: string) => {
    if (!businessProfile?.id) return;

    Alert.alert(
      'Delete reward',
      'Are you sure you want to delete this reward?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result =
                await BusinessService.deleteBusinessReward(rewardId);
              if (result.error) {
                setError(result.error);
              } else {
                await fetchRewards(businessProfile.id);
                Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Success
                );
              }
            } catch (error) {
              console.error('Error deleting reward:', error);
              setError('Failed to delete reward. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleSignOut = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Sign out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign out',
        style: 'destructive',
        onPress: async () => {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          const { error } = await signOut();
          if (!error) {
            router.replace('/(auth)/sign-in');
          }
        },
      },
    ]);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Settings
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='flex-1 px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading business profile...
              </Text>
            </VStack>
          ) : (
            <VStack space='xl' className='flex-1'>
              {/* User Profile Card */}
              <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                <HStack space='md' className='items-center'>
                  {/* Profile Icon */}
                  <Box className='w-16 h-16 bg-primary-500 rounded-2xl items-center justify-center'>
                    <FontAwesome name='user' size={24} color='white' />
                  </Box>

                  {/* User Info */}
                  <VStack className='flex-1'>
                    <Text
                      size='lg'
                      className='text-typography-900 font-semibold'
                    >
                      {user?.email || 'Not signed in'}
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Business Owner
                    </Text>
                  </VStack>
                </HStack>
              </Box>

              {/* Error Message */}
              {error && (
                <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
                  <HStack space='md' className='items-center'>
                    <FontAwesome
                      name='exclamation-triangle'
                      size={20}
                      color='#EF4444'
                    />
                    <Text
                      size='md'
                      className='text-error-700 font-semibold flex-1'
                    >
                      {error}
                    </Text>
                  </HStack>
                </Box>
              )}

              {/* Business Profile Card */}
              <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                <VStack space='lg'>
                  <HStack space='md' className='items-center justify-between'>
                    <HStack space='md' className='items-center'>
                      <FontAwesome name='building' size={24} color='#1f2937' />
                      <Heading
                        size='lg'
                        className='text-typography-900 font-semibold'
                      >
                        Business profile
                      </Heading>
                    </HStack>
                    {businessProfile && !editing && (
                      <Button
                        size='sm'
                        action='secondary'
                        onPress={() => {
                          Haptics.impactAsync(
                            Haptics.ImpactFeedbackStyle.Medium
                          );
                          setEditing(true);
                        }}
                      >
                        <ButtonText>Edit</ButtonText>
                      </Button>
                    )}
                  </HStack>

                  {editing ? (
                    // Edit mode
                    <VStack space='md'>
                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Business name *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='Enter your business name'
                            value={businessName}
                            onChangeText={setBusinessName}
                          />
                        </Input>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Business type *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='e.g., Restaurant, Retail, Gym'
                            value={businessType}
                            onChangeText={setBusinessType}
                          />
                        </Input>
                      </VStack>

                      <HStack space='md'>
                        <Button
                          size='lg'
                          action='secondary'
                          className='flex-1'
                          onPress={() => {
                            setEditing(false);
                            setError(null);
                            // Reset form to original values
                            if (businessProfile) {
                              setBusinessName(businessProfile.businessName);
                              setBusinessType(businessProfile.businessType);
                            }
                          }}
                        >
                          <ButtonText>Cancel</ButtonText>
                        </Button>
                        <Button
                          size='lg'
                          action='primary'
                          className='flex-1'
                          onPress={handleSaveProfile}
                          disabled={saving}
                        >
                          {saving ? (
                            <Spinner size='small' color='white' />
                          ) : (
                            <ButtonText>Save</ButtonText>
                          )}
                        </Button>
                      </HStack>
                    </VStack>
                  ) : businessProfile ? (
                    // View mode
                    <VStack space='md'>
                      <VStack space='sm'>
                        <Text
                          size='sm'
                          className='text-typography-600 font-medium'
                        >
                          Business name
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          {businessProfile.businessName}
                        </Text>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='sm'
                          className='text-typography-600 font-medium'
                        >
                          Business type
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          {businessProfile.businessType}
                        </Text>
                      </VStack>
                    </VStack>
                  ) : (
                    // No profile exists
                    <VStack space='md' className='items-center'>
                      <FontAwesome
                        name='building-o'
                        size={48}
                        color='#9CA3AF'
                      />
                      <Text
                        size='md'
                        className='text-typography-600 text-center'
                      >
                        No business profile found
                      </Text>
                      <Text
                        size='sm'
                        className='text-typography-500 text-center'
                      >
                        Create your business profile to start using the app
                      </Text>
                    </VStack>
                  )}
                </VStack>
              </Box>

              {/* Rewards Management Card */}
              {businessProfile && (
                <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                  <VStack space='lg'>
                    <HStack space='md' className='items-center justify-between'>
                      <HStack space='md' className='items-center'>
                        <FontAwesome name='gift' size={24} color='#1f2937' />
                        <Heading
                          size='lg'
                          className='text-typography-900 font-semibold'
                        >
                          Rewards
                        </Heading>
                      </HStack>
                      <Button
                        size='sm'
                        action='primary'
                        onPress={() => {
                          Haptics.impactAsync(
                            Haptics.ImpactFeedbackStyle.Medium
                          );
                          handleAddReward();
                        }}
                      >
                        <ButtonText>Add</ButtonText>
                      </Button>
                    </HStack>

                    {loadingRewards ? (
                      <VStack space='md' className='items-center py-4'>
                        <Spinner size='small' />
                        <Text size='sm' className='text-typography-600'>
                          Loading rewards...
                        </Text>
                      </VStack>
                    ) : rewards.length === 0 ? (
                      <VStack space='md' className='items-center py-4'>
                        <FontAwesome name='gift' size={48} color='#9CA3AF' />
                        <Text
                          size='md'
                          className='text-typography-600 text-center'
                        >
                          No rewards configured
                        </Text>
                        <Text
                          size='sm'
                          className='text-typography-500 text-center'
                        >
                          Add rewards to incentivize customer loyalty
                        </Text>
                      </VStack>
                    ) : (
                      <VStack space='md'>
                        {rewards.map(reward => (
                          <Box
                            key={reward.id}
                            className='bg-background-50 border-2 border-background-300 rounded-xl p-4'
                          >
                            <VStack space='sm'>
                              <HStack
                                space='md'
                                className='items-start justify-between'
                              >
                                <VStack space='xs' className='flex-1'>
                                  <Text
                                    size='md'
                                    className='text-typography-900 font-semibold'
                                  >
                                    {reward.title}
                                  </Text>
                                  <Text
                                    size='sm'
                                    className='text-typography-600'
                                  >
                                    {reward.description}
                                  </Text>
                                </VStack>
                                <HStack space='sm' className='items-center'>
                                  <Pressable
                                    onPress={() => {
                                      Haptics.impactAsync(
                                        Haptics.ImpactFeedbackStyle.Light
                                      );
                                      handleEditReward(reward);
                                    }}
                                    className='w-8 h-8 bg-yellow-500 rounded-lg items-center justify-center'
                                  >
                                    <FontAwesome
                                      name='edit'
                                      size={14}
                                      color='white'
                                    />
                                  </Pressable>
                                  <Pressable
                                    onPress={() => {
                                      Haptics.impactAsync(
                                        Haptics.ImpactFeedbackStyle.Light
                                      );
                                      handleDeleteReward(reward.id);
                                    }}
                                    className='w-8 h-8 bg-error-500 rounded-lg items-center justify-center'
                                  >
                                    <FontAwesome
                                      name='trash'
                                      size={14}
                                      color='white'
                                    />
                                  </Pressable>
                                </HStack>
                              </HStack>
                              <HStack space='md' className='items-center'>
                                <FontAwesome
                                  name='star'
                                  size={16}
                                  color='#F59E0B'
                                />
                                <Text
                                  size='sm'
                                  className='text-typography-700 font-medium'
                                >
                                  {reward.pointsRequired} points required
                                </Text>
                              </HStack>
                            </VStack>
                          </Box>
                        ))}
                      </VStack>
                    )}
                  </VStack>
                </Box>
              )}

              {/* Add/Edit Reward Modal */}
              {showAddReward && (
                <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                  <VStack space='lg'>
                    <Heading
                      size='lg'
                      className='text-typography-900 font-semibold'
                    >
                      {editingReward ? 'Edit reward' : 'Add new reward'}
                    </Heading>

                    <VStack space='md'>
                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Reward title *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='e.g., £5 off'
                            value={rewardTitle}
                            onChangeText={setRewardTitle}
                          />
                        </Input>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Description *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='e.g., £5 off your next transaction'
                            value={rewardDescription}
                            onChangeText={setRewardDescription}
                          />
                        </Input>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Points required *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='e.g., 100'
                            value={rewardPoints}
                            onChangeText={setRewardPoints}
                            keyboardType='numeric'
                          />
                        </Input>
                      </VStack>

                      <HStack space='md'>
                        <Button
                          size='lg'
                          action='secondary'
                          className='flex-1'
                          onPress={() => {
                            setShowAddReward(false);
                            setEditingReward(null);
                          }}
                        >
                          <ButtonText>Cancel</ButtonText>
                        </Button>
                        <Button
                          size='lg'
                          action='primary'
                          className='flex-1'
                          onPress={handleSaveReward}
                          disabled={saving}
                        >
                          {saving ? (
                            <Spinner size='small' color='white' />
                          ) : (
                            <ButtonText>Confirm</ButtonText>
                          )}
                        </Button>
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>
              )}

              {/* Sign Out Button */}
              <Button
                size='lg'
                onPress={handleSignOut}
                className='w-full bg-error-500 rounded-xl border-2 border-error-700 shadow-lg'
              >
                <ButtonText className='text-white font-semibold text-lg'>
                  Sign out
                </ButtonText>
              </Button>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
